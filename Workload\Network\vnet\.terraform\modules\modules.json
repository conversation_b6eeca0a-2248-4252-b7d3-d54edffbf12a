{"Modules": [{"Key": "", "Source": "", "Dir": "."}, {"Key": "aks", "Source": "../../../Modules/AKS/aks", "Dir": "../../../Modules/AKS/aks"}, {"Key": "bastion", "Source": "../../../Modules/Network/bastion", "Dir": "../../../Modules/Network/bastion"}, {"Key": "cosmosDb", "Source": "../../../Modules/cosmos", "Dir": "../../../Modules/cosmos"}, {"Key": "nodePools", "Source": "../../../Modules/AKS/nodePool", "Dir": "../../../Modules/AKS/nodePool"}, {"Key": "nsg", "Source": "../../../Modules/Network/networkSecurityGroup", "Dir": "../../../Modules/Network/networkSecurityGroup"}, {"Key": "nsgAssociation", "Source": "../../../Modules/Network/Vnet/association/NSG", "Dir": "../../../Modules/Network/Vnet/association/NSG"}, {"Key": "pip", "Source": "../../../Modules/Network/publicIp", "Dir": "../../../Modules/Network/publicIp"}, {"Key": "routeTable", "Source": "../../../Modules/Network/routeTable", "Dir": "../../../Modules/Network/routeTable"}, {"Key": "rtAssociation", "Source": "../../../Modules/Network/Vnet/association/RT", "Dir": "../../../Modules/Network/Vnet/association/RT"}, {"Key": "subnets", "Source": "../../../Modules/Network/Vnet/subnet", "Dir": "../../../Modules/Network/Vnet/subnet"}, {"Key": "subnets2", "Source": "../../../Modules/Network/Vnet/subnet", "Dir": "../../../Modules/Network/Vnet/subnet"}, {"Key": "virtualNetwork1", "Source": "../../../Modules/Network/Vnet/virtualNetwork", "Dir": "../../../Modules/Network/Vnet/virtualNetwork"}, {"Key": "virtualNetwork2", "Source": "../../../Modules/Network/Vnet/virtualNetwork", "Dir": "../../../Modules/Network/Vnet/virtualNetwork"}, {"Key": "virtualNetworkPeering", "Source": "../../../Modules/Network/Vnet/vnetPeering", "Dir": "../../../Modules/Network/Vnet/vnetPeering"}, {"Key": "virtualNetworkPeeringReverse", "Source": "../../../Modules/Network/Vnet/vnetPeering", "Dir": "../../../Modules/Network/Vnet/vnetPeering"}]}