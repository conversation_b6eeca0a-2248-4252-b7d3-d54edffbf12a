module "virtualNetwork1" {
  source                     = "../../../Modules/Network/Vnet/virtualNetwork" // Modifiy module path accordingly 
  virtualNetworkName         = "Demo-VNet1"
  rgName                     = "kartik.jindal_RG"
  location                   = "Central India"
  virtualNetworkAddressSpace = ["10.0.0.0/16"]
  tags = {
    Environment = "Production"
    Department  = "Cloud Infra & Security"
  }
}
module "virtualNetwork2" {
  source                     = "../../../Modules/Network/Vnet/virtualNetwork" // Modifiy module path accordingly 
  virtualNetworkName         = "Demo-VNet"
  rgName                     = "kartik.jindal_RG"
  location                   = "Central India"
  virtualNetworkAddressSpace = ["********/16"]
  tags = {
    Environment = "Production"
    Department  = "Cloud Infra & Security"
  }
}
module "virtualNetworkPeering" {
  source = "../../../Modules/Network/Vnet/vnetPeering" // Modify module path accordingly 

  // Required parameters as separate variables
  peeringName              = "vnet1-to-vnet2"
  resourceGroupName        = "kartik.jindal_RG"
  sourceVirtualNetworkName = "Demo-VNet1"
  remoteVirtualNetworkId   = module.virtualNetwork2.virtualNetworkId

  // Optional parameters as an object
  peeringOptions = {
    allowVirtualNetworkAccess = true
    allowForwardedTraffic     = false
    allowGatewayTransit       = false
    useRemoteGateways         = false
  }
  depends_on = [ module.virtualNetwork1 , module.virtualNetwork2 ]
}
module "virtualNetworkPeeringReverse" {
  source = "../../../Modules/Network/Vnet/vnetPeering" // Modify module path accordingly 

  // Required parameters as separate variables
  peeringName              = "vnet2-to-vnet1"
  resourceGroupName        = "kartik.jindal_RG"
  sourceVirtualNetworkName = "Demo-VNet2"
  remoteVirtualNetworkId   = module.virtualNetwork1.virtualNetworkId

  // You can specify only the options you want to change from defaults
  peeringOptions = {
    allowForwardedTraffic = true
  }
  depends_on = [ module.virtualNetwork1 , module.virtualNetwork2 ]
}
module "subnets" {
  source                                   = "../../../Modules/Network/Vnet/subnet" // Modifiy module path accordingly 
  subnetName                               = "Demo-Subnet"
  rgName                                   = "kartik.jindal_RG"
  subnetAddressPrefixes                    = ["********/24"]
  virtualNetworkName                       = "Demo-Vnet1" // Associate Virtual Network Name on this Place with the help of either Module (using Output), Locals or Resource Block
  privateLinkServiceNetworkPoliciesEnabled = true
  subnetDelegations = {
    subnetDelegationName  = "appservice-delegation"
    serviceDelegationName = "Microsoft.Web/serverFarms"
    actions               = "Microsoft.Network/virtualNetworks/subnets/action"
  }
  subnetNsgAssociation = false
  subnetRtAssociation  = false
  depends_on = [ module.virtualNetwork1 ]
}
module "subnets2" {
  source                                   = "../../../Modules/Network/Vnet/subnet" // Modifiy module path accordingly 
  subnetName                               = "Demo-Subnet"
  rgName                                   = "kartik.jindal_RG"
  subnetAddressPrefixes                    = ["********/24"]
  virtualNetworkName                       = "Demo-Vnet2" // Associate Virtual Network Name on this Place with the help of either Module (using Output), Locals or Resource Block
  privateLinkServiceNetworkPoliciesEnabled = true
  subnetDelegations = {
    subnetDelegationName  = "appservice-delegation"
    serviceDelegationName = "Microsoft.Web/serverFarms"
    actions               = "Microsoft.Network/virtualNetworks/subnets/action"
  }
  subnetNsgAssociation = false
  subnetRtAssociation  = false
  depends_on = [ module.virtualNetwork2 ]
}
module "nsg" {
  source   = "../../../Modules/Network/networkSecurityGroup" // Modifiy module path accordingly 
  nsgName  = "Demo-NSG"
  location = "Central India"
  rgName   = "kartik.jindal_RG"
  tags = {
    Environment = "Production"
    Department  = "Cloud Infra & Security"
  }
  secRule = [
    {
      name                     = "Demo-Rule"
      protocol                 = "Tcp"
      sourcePortRange          = "*"
      destinationPortRange     = "*"
      sourceAddressPrefix      = "*"
      destinationAddressPrefix = "*"
      access                   = "Allow"
      priority                 = 100
      direction                = "Inbound"
    }
  ]
}
module "nsgAssociation" {
  source   = "../../../Modules/Network/Vnet/association/NSG" // Modifiy module path accordingly 
  nsgId    = module.nsg.nsg_id
  subnetId = module.subnets.subnetId
  depends_on = [ module.subnets ]
}
module "routeTable" {
  source                     = "../../../Modules/Network/routeTable" // Modifiy module path accordingly 
  rtName                     = "DemoRouteTable"
  location                   = "Central India"
  rgName                     = "kartik.jindal_RG"
  disableBgpRoutePropagation = false
  tags = {
    Environment = "Production"
    Department  = "Cloud Infra & Security"
  }
  rtRoutes = [
    {
      name               = "DemoRoute"
      addressPrefix      = "********/16"
      nextHopType        = "VirtualAppliance"
      nextHopInIpAddress = "********/16"
    }
  ]
}
module "rtAssociation" {
  source   = "../../../Modules/Network/Vnet/association/RT" // Modifiy module path accordingly 
  rtId     = module.routeTable.routeTableId
  subnetId = module.subnets.subnetId
  depends_on = [ module.subnets ] 
}
module "aks" {
  source                          = "../../../Modules/AKS/aks" // Modifiy module path accordingly 
  aksName                         = "AKS"
  location                        = "Central India"
  rgName                          = "kartik.jindal_RG"
  skuTier                         = "Standard"
  privateClusterEnabled           = false
  privateClusterPublicFqdnEnabled = false
  dnsPrefix                       = "aksdemoprefix"
  node_resource_group             = "AKS-kartik.jindal_RG"
  networkPlugin                   = "azure"
  serviceCidr                     = "10.0.0.0/26"
  dnsServiceIp                    = "********"
  nodePoolName                    = "systempool"
  vnetId                          = module.subnets2.subnetId // Associate Virtual Network Id on this Place with the help of either Module, Locals or Resource Block
  vmSize                          = "Standard_Ds2_v2"
  osSku                           = "Ubuntu"
  osDiskType                      = "Managed"
  osDiskSizeGb                    = 128
  nodePoolZones                   = ["1", "2", "3"]
  nodeCount                       = 3
  enableAutoScaling               = true
  systemMaxCount                  = 5
  systemMinCount                  = 1
  systemMaxPods                   = 30
  identityType                    = "SystemAssigned"
  aksTags = {
    Environment = "Production"
    Department  = "Cloud Infra & Security"
  }
  depends_on = [ module.subnets2 ]
}
module "nodePools" {
  source                = "../../../Modules/AKS/nodePool"
  kubernetes_cluster_id = module.aks.aksId         // Associate Kubernetes Cluster ID on this Place with the help of either Module, Locals or Resource Block
  vnetId                = module.subnets2.subnetId // Associate Virtual Network Id on this Place with the help of either Module, Locals or Resource Block
  nodePool = {
    userPool = {
      name                = "userpool"
      mode                = "User"
      vm_size             = "Standard_DS2_v2"
      os_type             = "Linux"
      os_sku              = "Ubuntu"
      os_disk_type        = "Managed"
      os_disk_size_gb     = 128
      enable_auto_scaling = true
      node_count          = 3
      max_count           = 5
      min_count           = 1
      zones               = ["1", "2", "3"]
      max_pods            = 30
      node_labels = {
        "environment" = "production"
        "workload"    = "general"
      }
    }
  }
  depends_on = [ module.aks ]
}
module "pip" {
  source           = "../../../Modules/Network/publicIp" // Modifiy module path accordingly 
  publicIpName     = "Demo-IP"
  location         = "Central India"
  rgName           = "kartik.jindal_RG"
  allocationMethod = "Dynamic"
  publicIpSKU      = "Standard"
  zones            = ["1"]
  tags = {
    Environment = "Production"
    Department  = "Cloud Infra & Security"
  }
}
module "bastion" {
  source   = "../../../Modules/Network/bastion" // Modify module path accordingly 
  location = "Central India"
  rgName   = "kartik.jindal_RG"

  bastionSettings = {
    bastionName         = "Demo-Bastion"
    sku                 = "Standard"
    ipConfigurationName = "configuration"
    scaleUnits          = 2
  }

  bastionConfig = {
    copyPasteEnabled = true
    fileCopyEnabled  = true
    tunnelingEnabled = true
  }

  subnetId          = module.subnets.subnetId
  publicIpAddressId = module.pip.publicIpId

  tags = {
    Environment = "Production"
    Department  = "Cloud Infra & Security"
  }
  depends_on = [ module.subnets, module.pip ]
}
module "cosmosDb" {
  source   = "../../../Modules/cosmos" // Modify module path accordingly 
  location = "Central India"
  rgName   = "kartik.jindal_RG"

  cosmosSettings = {
    accountName = "demo-cosmos-account"
    kind        = "GlobalDocumentDB"
    offerType   = "Standard"
  }

  cosmosConfig = {
    publicNetworkAccessEnabled = true
    analyticalStorageEnabled   = false
  }

  consistencyPolicy = {
    level = "Session"
  }

  geoLocations = [
    {
      location         = "Central India"
      failoverPriority = 0
      zoneRedundant    = false
    },
    {
      location         = "East US"
      failoverPriority = 1
      zoneRedundant    = false
    }
  ]

  capabilities = [
    "EnableServerless",
    "EnableAggregationPipeline"
  ]

  backupPolicy = {
    type              = "Periodic"
    intervalInMinutes = 240
    retentionInHours  = 8
  }

  sqlDatabases = {
    "demo-database" = {
      maxThroughput = 4000
    }
  }

  sqlContainers = {
    "container1" = {
      name             = "users"
      databaseName     = "demo-database"
      partitionKeyPath = "/id"
      maxThroughput    = 4000
      indexingPolicy = {
        indexingMode  = "consistent"
        includedPaths = ["/name/*", "/address/*"]
        excludedPaths = ["/temp/*"]
      }
      uniqueKeys = [
        ["/email"]
      ]
    },
    "container2" = {
      name             = "products"
      databaseName     = "demo-database"
      partitionKeyPath = "/category"
      maxThroughput    = 4000
    }
  }

  tags = {
    Environment = "Production"
    Department  = "Cloud Infra & Security"
  }
}