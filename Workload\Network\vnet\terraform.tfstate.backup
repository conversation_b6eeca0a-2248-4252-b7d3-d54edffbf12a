{"version": 4, "terraform_version": "1.10.3", "serial": 1, "lineage": "a3fac8e2-223b-fe6d-34d6-75a7b98b5a59", "outputs": {}, "resources": [], "check_results": [{"object_kind": "var", "config_addr": "module.bastion.var.bastionSettings", "status": "pass", "objects": [{"object_addr": "module.bastion.var.bastionSettings", "status": "pass"}]}, {"object_kind": "var", "config_addr": "module.cosmosDb.var.cosmosSettings", "status": "pass", "objects": [{"object_addr": "module.cosmosDb.var.cosmosSettings", "status": "pass"}]}, {"object_kind": "var", "config_addr": "module.cosmosDb.var.consistencyPolicy", "status": "pass", "objects": [{"object_addr": "module.cosmosDb.var.consistencyPolicy", "status": "pass"}]}]}